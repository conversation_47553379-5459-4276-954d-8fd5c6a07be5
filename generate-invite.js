require('dotenv').config();

// Tạo invite link cho bot với đầy đủ permissions
const APP_ID = process.env.YOUR_APP_ID;

// Permissions cần thiết cho bot
const permissions = [
    'ViewChannels',           // Xem kênh
    'SendMessages',           // <PERSON><PERSON><PERSON> tin nhắn  
    'UseSlashCommands',       // Sử dụng slash commands
    'ReadMessageHistory',     // <PERSON><PERSON><PERSON> l<PERSON><PERSON> sử tin nhắn
    'EmbedLinks',            // Nhúng links
    'AttachFiles',           // Đính kèm files
    'UseExternalEmojis',     // Sử dụng emoji ngoài
    'AddReactions',          // Thêm reactions
    'ManageMessages',        // Quản lý tin nhắn (xóa tin nhắn của bot)
].join('%20');

// Tính toán permission value
const permissionValue = 
    0x0000000000000400 + // VIEW_CHANNEL
    0x0000000000000800 + // SEND_MESSAGES  
    0x0000000000004000 + // EMBED_LINKS
    0x0000000000008000 + // ATTACH_FILES
    0x0000000000010000 + // READ_MESSAGE_HISTORY
    0x0000000000040000 + // USE_EXTERNAL_EMOJIS
    0x0000000000000040 + // ADD_REACTIONS
    0x0000000000002000 + // MANAGE_MESSAGES
    0x0000080000000000;  // USE_SLASH_COMMANDS

console.log('🤖 INVITE LINK CHO BOT:');
console.log('='.repeat(80));

// Link với permissions tính toán
const inviteLink1 = `https://discord.com/api/oauth2/authorize?client_id=${APP_ID}&permissions=${permissionValue}&scope=bot%20applications.commands`;

// Link với permissions cơ bản (backup)
const inviteLink2 = `https://discord.com/api/oauth2/authorize?client_id=${APP_ID}&permissions=2147483648&scope=bot%20applications.commands`;

// Link chỉ với bot scope (minimal)
const inviteLink3 = `https://discord.com/api/oauth2/authorize?client_id=${APP_ID}&scope=bot%20applications.commands`;

console.log('📋 LINK 1 (Đầy đủ permissions - Khuyến nghị):');
console.log(inviteLink1);
console.log('');

console.log('📋 LINK 2 (Permissions cơ bản):');
console.log(inviteLink2);
console.log('');

console.log('📋 LINK 3 (Minimal permissions):');
console.log(inviteLink3);
console.log('');

console.log('🔧 HƯỚNG DẪN:');
console.log('1. Copy một trong các link trên');
console.log('2. Dán vào trình duyệt');
console.log('3. Chọn server muốn thêm bot');
console.log('4. Authorize bot');
console.log('');

console.log('⚠️  LƯU Ý:');
console.log('- Nếu bot đã có trong server, hãy kick bot trước rồi invite lại');
console.log('- Đảm bảo bạn có quyền "Manage Server" trong Discord server');
console.log('- Sau khi invite, bot sẽ xuất hiện trong danh sách thành viên');
