# 🔧 Hướng dẫn thiết lập Bot Discord

## ❗ Khắc phục vấn đề "Bot không hiển thị trong danh sách thành viên"

### Bước 1: Enable Guild Members Intent
1. **Truy cập Discord Developer Portal:**
   - Vào https://discord.com/developers/applications
   - Đăng nhập với tài khoản Discord của bạn
   - Chọn application của bot (DQX Bot)

2. **Enable Intent:**
   - Click vào tab **"Bot"** ở sidebar bên trái
   - Scroll xuống phần **"Privileged Gateway Intents"**
   - **BẬT** (toggle ON) **"Server Members Intent"**
   - Click **"Save Changes"**

3. **Cập nhật code:**
   - Mở file `index.js`
   - Uncomment dòng `GatewayIntentBits.GuildMembers`:
   ```javascript
   const client = new Client({
       intents: [
           GatewayIntentBits.Guilds,
           GatewayIntentBits.GuildMembers, // Uncomment dòng này
       ],
   });
   ```

4. **Restart bot:**
   - Dừng bot (Ctrl+C)
   - Chạy lại: `node index.js`

### Bước 2: Kiểm tra Permissions trong Server
1. **Vào Server Settings:**
   - Right-click vào tên server → Server Settings
   - Chọn **"Roles"** ở sidebar

2. **Kiểm tra role của bot:**
   - Tìm role của bot (thường có tên giống bot)
   - Đảm bảo có các permissions:
     - ✅ View Channels
     - ✅ Send Messages
     - ✅ Use Slash Commands
     - ✅ Read Message History

3. **Invite bot với đúng permissions:**
   - Nếu bot chưa có đủ quyền, re-invite với link:
   ```
   https://discord.com/api/oauth2/authorize?client_id=YOUR_APP_ID&permissions=2147483648&scope=bot%20applications.commands
   ```
   - Thay `YOUR_APP_ID` bằng Application ID của bạn

## ✅ Kiểm tra bot hoạt động

Sau khi làm theo các bước trên:

1. **Bot sẽ hiển thị trong danh sách thành viên**
2. **Commands sẽ hoạt động bình thường:**
   - `/ping` - Kiểm tra độ trễ
   - `/info` - Thông tin (đã fix lỗi iconURL)
   - `/help` - Danh sách lệnh

## 🚨 Troubleshooting

### Lỗi "Used disallowed intents"
- Chưa enable Guild Members Intent trong Developer Portal
- Làm theo Bước 1 ở trên

### Bot không phản hồi slash commands
- Commands chưa được deploy: `node deploy-commands.js`
- Bot chưa có permission "Use Slash Commands"

### Lỗi "Cannot read properties of null"
- Đã được fix trong `commands/info.js`
- Server không có icon nên cần check trước khi dùng

## 📞 Liên hệ hỗ trợ

Nếu vẫn gặp vấn đề, hãy kiểm tra:
1. Bot token có đúng không
2. Bot đã được invite vào server chưa
3. Có lỗi nào trong console không
